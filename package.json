{"name": "llm-rag-chatbot-embed", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@rollup/plugin-terser": "^0.4.4", "@tailwindcss/vite": "^4.1.3", "@types/markdown-it": "^14.1.2", "animate.css": "^4.1.1", "axios": "^1.8.4", "date-fns": "^4.1.0", "markdown-it": "^14.1.0", "markdown-it-link-attributes": "^4.0.1", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "vite-plugin-css-injected-by-js": "^3.5.2", "vue": "^3.5.13", "vue-router": "4"}, "devDependencies": {"@iconify/vue": "^4.3.0", "@tsconfig/node22": "^22.0.1", "@types/markdown-it-link-attributes": "^3.0.5", "@types/node": "^22.13.14", "@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "npm-run-all2": "^7.0.2", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "postcss-nested": "^7.0.2", "postcss-nesting": "^13.0.1", "postcss-prefixwrap": "^1.55.0", "prettier": "3.5.3", "sass-embedded": "^1.86.3", "tailwindcss": "3.4.7", "terser": "^5.39.0", "tsx": "^4.19.3", "typescript": "~5.8.2", "vite": "^6.2.4", "vite-plugin-obfuscator": "^1.0.5", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@8.9.2+sha1.5f2fa48d614263457cf5d7fb7be8b878da318d87"}